"""
Analytics Validator for Wiz Aroma Delivery System
Validates and ensures accuracy of analytics calculations and reporting data
"""

import datetime
import logging
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal, ROUND_HALF_UP

from src.firebase_db import get_data, set_data, update_data
from src.config import logger


class AnalyticsValidator:
    """Validates analytics calculations and ensures data accuracy"""
    
    def __init__(self):
        self.logger = logger
        self.validation_errors = []
        self.calculation_cache = {}
    
    def validate_revenue_calculations(self) -> Dict[str, Any]:
        """Validate revenue calculations across all time periods"""
        try:
            self.logger.info("Starting revenue calculations validation...")
            
            # Get fresh data from Firebase
            completed_orders = get_data("completed_orders") or {}
            confirmed_orders = get_data("confirmed_orders") or {}
            
            validation_results = {
                "total_orders_validated": len(completed_orders),
                "revenue_validation": {},
                "earnings_validation": {},
                "calculation_errors": [],
                "summary": {}
            }
            
            # Validate daily revenue calculations
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            daily_validation = self._validate_daily_revenue(completed_orders, today)
            validation_results["revenue_validation"]["daily"] = daily_validation
            
            # Validate weekly revenue calculations
            weekly_validation = self._validate_weekly_revenue(completed_orders)
            validation_results["revenue_validation"]["weekly"] = weekly_validation
            
            # Validate monthly revenue calculations
            monthly_validation = self._validate_monthly_revenue(completed_orders)
            validation_results["revenue_validation"]["monthly"] = monthly_validation
            
            # Validate all-time revenue calculations
            alltime_validation = self._validate_alltime_revenue(completed_orders)
            validation_results["revenue_validation"]["all_time"] = alltime_validation
            
            # Validate delivery personnel earnings
            earnings_validation = self._validate_personnel_earnings(completed_orders)
            validation_results["earnings_validation"] = earnings_validation
            
            # Generate summary
            validation_results["summary"] = self._generate_validation_summary(validation_results)
            
            self.logger.info("Revenue calculations validation completed")
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Error during revenue validation: {e}")
            return {"error": str(e)}
    
    def _validate_daily_revenue(self, completed_orders: Dict[str, Any], target_date: str) -> Dict[str, Any]:
        """Validate daily revenue calculations"""
        try:
            # Filter orders for target date
            daily_orders = [
                order for order in completed_orders.values()
                if isinstance(order, dict) and order.get('completed_at', '').startswith(target_date)
            ]
            
            # Calculate revenue with high precision
            food_revenue = Decimal('0')
            delivery_revenue = Decimal('0')
            
            for order in daily_orders:
                try:
                    subtotal = Decimal(str(order.get('subtotal', 0)))
                    delivery_fee = Decimal(str(order.get('delivery_fee', 0)))
                    
                    food_revenue += subtotal
                    delivery_revenue += delivery_fee
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Invalid numeric value in order {order.get('order_number', 'unknown')}: {e}")
            
            total_revenue = food_revenue + delivery_revenue
            
            # Calculate earnings (50% of delivery fees)
            personnel_earnings = delivery_revenue * Decimal('0.5')
            company_profit = delivery_revenue * Decimal('0.5')
            
            return {
                "date": target_date,
                "orders_count": len(daily_orders),
                "food_revenue": float(food_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "delivery_revenue": float(delivery_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "total_revenue": float(total_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "personnel_earnings": float(personnel_earnings.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "company_profit": float(company_profit.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "validation_status": "passed"
            }
            
        except Exception as e:
            self.logger.error(f"Error validating daily revenue for {target_date}: {e}")
            return {"validation_status": "failed", "error": str(e)}
    
    def _validate_weekly_revenue(self, completed_orders: Dict[str, Any]) -> Dict[str, Any]:
        """Validate weekly revenue calculations"""
        try:
            # Get current week boundaries
            today = datetime.datetime.now()
            week_start = today - datetime.timedelta(days=today.weekday())
            week_end = week_start + datetime.timedelta(days=6)
            
            # Filter orders for current week
            weekly_orders = []
            for order in completed_orders.values():
                if isinstance(order, dict):
                    completed_at = order.get('completed_at', '')
                    if completed_at:
                        try:
                            order_date = datetime.datetime.strptime(completed_at[:10], '%Y-%m-%d')
                            if week_start <= order_date <= week_end:
                                weekly_orders.append(order)
                        except ValueError:
                            continue
            
            # Calculate weekly totals
            food_revenue = sum(Decimal(str(order.get('subtotal', 0))) for order in weekly_orders)
            delivery_revenue = sum(Decimal(str(order.get('delivery_fee', 0))) for order in weekly_orders)
            total_revenue = food_revenue + delivery_revenue
            
            return {
                "week_start": week_start.strftime("%Y-%m-%d"),
                "week_end": week_end.strftime("%Y-%m-%d"),
                "orders_count": len(weekly_orders),
                "food_revenue": float(food_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "delivery_revenue": float(delivery_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "total_revenue": float(total_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "personnel_earnings": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "company_profit": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "validation_status": "passed"
            }
            
        except Exception as e:
            self.logger.error(f"Error validating weekly revenue: {e}")
            return {"validation_status": "failed", "error": str(e)}
    
    def _validate_monthly_revenue(self, completed_orders: Dict[str, Any]) -> Dict[str, Any]:
        """Validate monthly revenue calculations"""
        try:
            # Get current month boundaries
            today = datetime.datetime.now()
            month_start = today.replace(day=1)
            
            # Filter orders for current month
            monthly_orders = []
            for order in completed_orders.values():
                if isinstance(order, dict):
                    completed_at = order.get('completed_at', '')
                    if completed_at and completed_at.startswith(today.strftime("%Y-%m")):
                        monthly_orders.append(order)
            
            # Calculate monthly totals
            food_revenue = sum(Decimal(str(order.get('subtotal', 0))) for order in monthly_orders)
            delivery_revenue = sum(Decimal(str(order.get('delivery_fee', 0))) for order in monthly_orders)
            total_revenue = food_revenue + delivery_revenue
            
            return {
                "month": today.strftime("%Y-%m"),
                "orders_count": len(monthly_orders),
                "food_revenue": float(food_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "delivery_revenue": float(delivery_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "total_revenue": float(total_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "personnel_earnings": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "company_profit": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "validation_status": "passed"
            }
            
        except Exception as e:
            self.logger.error(f"Error validating monthly revenue: {e}")
            return {"validation_status": "failed", "error": str(e)}
    
    def _validate_alltime_revenue(self, completed_orders: Dict[str, Any]) -> Dict[str, Any]:
        """Validate all-time revenue calculations"""
        try:
            # Calculate all-time totals
            food_revenue = Decimal('0')
            delivery_revenue = Decimal('0')
            
            for order in completed_orders.values():
                if isinstance(order, dict):
                    try:
                        food_revenue += Decimal(str(order.get('subtotal', 0)))
                        delivery_revenue += Decimal(str(order.get('delivery_fee', 0)))
                    except (ValueError, TypeError):
                        continue
            
            total_revenue = food_revenue + delivery_revenue
            
            return {
                "orders_count": len(completed_orders),
                "food_revenue": float(food_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "delivery_revenue": float(delivery_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "total_revenue": float(total_revenue.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "personnel_earnings": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "company_profit": float((delivery_revenue * Decimal('0.5')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)),
                "validation_status": "passed"
            }
            
        except Exception as e:
            self.logger.error(f"Error validating all-time revenue: {e}")
            return {"validation_status": "failed", "error": str(e)}
    
    def _validate_personnel_earnings(self, completed_orders: Dict[str, Any]) -> Dict[str, Any]:
        """Validate delivery personnel earnings calculations"""
        try:
            # Get assignments and earnings data
            assignments = get_data("delivery_personnel_assignments") or {}
            earnings_data = get_data("delivery_personnel_earnings") or {}
            
            validation_results = {
                "personnel_validations": {},
                "total_calculated_earnings": 0,
                "total_recorded_earnings": 0,
                "discrepancies": []
            }
            
            # Calculate expected earnings per personnel
            personnel_expected_earnings = {}
            
            for assignment_id, assignment in assignments.items():
                if isinstance(assignment, dict) and assignment.get('status') == 'delivered':
                    personnel_id = assignment.get('personnel_id')
                    order_number = assignment.get('order_number')
                    
                    if personnel_id and order_number:
                        # Find corresponding completed order
                        for order in completed_orders.values():
                            if isinstance(order, dict) and order.get('order_number') == order_number:
                                delivery_fee = Decimal(str(order.get('delivery_fee', 0)))
                                expected_earning = delivery_fee * Decimal('0.5')
                                
                                if personnel_id not in personnel_expected_earnings:
                                    personnel_expected_earnings[personnel_id] = Decimal('0')
                                
                                personnel_expected_earnings[personnel_id] += expected_earning
                                break
            
            # Compare with recorded earnings
            for personnel_id, expected_earnings in personnel_expected_earnings.items():
                recorded_earnings_data = earnings_data.get(personnel_id, {})
                recorded_total = Decimal(str(recorded_earnings_data.get('total_lifetime_earnings', 0)))
                
                expected_float = float(expected_earnings.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
                recorded_float = float(recorded_total.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
                
                validation_results["personnel_validations"][personnel_id] = {
                    "expected_earnings": expected_float,
                    "recorded_earnings": recorded_float,
                    "difference": abs(expected_float - recorded_float),
                    "status": "match" if abs(expected_float - recorded_float) < 0.01 else "mismatch"
                }
                
                if abs(expected_float - recorded_float) >= 0.01:
                    validation_results["discrepancies"].append({
                        "personnel_id": personnel_id,
                        "expected": expected_float,
                        "recorded": recorded_float,
                        "difference": expected_float - recorded_float
                    })
            
            validation_results["total_calculated_earnings"] = sum(
                v["expected_earnings"] for v in validation_results["personnel_validations"].values()
            )
            validation_results["total_recorded_earnings"] = sum(
                v["recorded_earnings"] for v in validation_results["personnel_validations"].values()
            )
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Error validating personnel earnings: {e}")
            return {"validation_status": "failed", "error": str(e)}
    
    def _generate_validation_summary(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate validation summary"""
        try:
            summary = {
                "overall_status": "passed",
                "total_validations": 0,
                "passed_validations": 0,
                "failed_validations": 0,
                "warnings": []
            }
            
            # Count revenue validations
            for period, result in validation_results.get("revenue_validation", {}).items():
                summary["total_validations"] += 1
                if result.get("validation_status") == "passed":
                    summary["passed_validations"] += 1
                else:
                    summary["failed_validations"] += 1
                    summary["overall_status"] = "failed"
            
            # Check earnings discrepancies
            earnings_validation = validation_results.get("earnings_validation", {})
            discrepancies = earnings_validation.get("discrepancies", [])
            
            if discrepancies:
                summary["warnings"].append(f"Found {len(discrepancies)} earnings discrepancies")
                if len(discrepancies) > 5:  # More than 5 discrepancies is concerning
                    summary["overall_status"] = "warning"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error generating validation summary: {e}")
            return {"overall_status": "error", "error": str(e)}
    
    def fix_earnings_discrepancies(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Fix earnings discrepancies automatically"""
        try:
            earnings_validation = validation_results.get("earnings_validation", {})
            discrepancies = earnings_validation.get("discrepancies", [])
            
            fix_results = {"fixed": [], "failed": []}
            
            for discrepancy in discrepancies:
                personnel_id = discrepancy["personnel_id"]
                expected_earnings = discrepancy["expected"]
                
                try:
                    # Update earnings record
                    from src.utils.earnings_utils import get_or_create_personnel_earnings
                    earnings = get_or_create_personnel_earnings(personnel_id)
                    
                    # Set correct total lifetime earnings
                    earnings.total_lifetime_earnings = expected_earnings
                    
                    # Save to Firebase
                    earnings_data = get_data("delivery_personnel_earnings") or {}
                    earnings_data[personnel_id] = earnings.to_dict()
                    
                    success = update_data("delivery_personnel_earnings", earnings_data)
                    
                    if success:
                        fix_results["fixed"].append(personnel_id)
                        self.logger.info(f"Fixed earnings for personnel {personnel_id}: {expected_earnings}")
                    else:
                        fix_results["failed"].append(personnel_id)
                        
                except Exception as e:
                    self.logger.error(f"Error fixing earnings for personnel {personnel_id}: {e}")
                    fix_results["failed"].append(personnel_id)
            
            return fix_results
            
        except Exception as e:
            self.logger.error(f"Error fixing earnings discrepancies: {e}")
            return {"error": str(e)}


# Global instance
analytics_validator = AnalyticsValidator()
