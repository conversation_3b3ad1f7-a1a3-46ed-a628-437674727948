#!/usr/bin/env python3
"""
System Improvements Validation Script
Validates that all key improvements have been properly implemented
"""

import os
import sys
import inspect
import importlib.util

def check_file_exists(filepath):
    """Check if a file exists"""
    return os.path.exists(filepath)

def check_function_exists(module_path, function_name):
    """Check if a function exists in a module"""
    try:
        spec = importlib.util.spec_from_file_location("module", module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return hasattr(module, function_name)
    except Exception:
        return False

def check_class_exists(module_path, class_name):
    """Check if a class exists in a module"""
    try:
        spec = importlib.util.spec_from_file_location("module", module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return hasattr(module, class_name)
    except Exception:
        return False

def validate_signal_handling_improvements():
    """Validate signal handling improvements"""
    print("🔧 Validating Signal Handling Improvements...")
    
    checks = []
    
    # Check if main.py has interruptible_sleep function
    checks.append({
        "name": "interruptible_sleep function",
        "result": check_function_exists("main.py", "interruptible_sleep")
    })

    # Check if shutdown_event is defined
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
            checks.append({
                "name": "shutdown_event definition",
                "result": "shutdown_event = threading.Event()" in content
            })
    except UnicodeDecodeError:
        checks.append({
            "name": "shutdown_event definition",
            "result": False
        })
    
    # Check enhanced signal_handler
    checks.append({
        "name": "enhanced signal_handler",
        "result": check_function_exists("main.py", "signal_handler")
    })
    
    # Check for interruptible bot startup
    checks.append({
        "name": "interruptible bot startup",
        "result": "interruptible_sleep(10)" in content
    })
    
    return checks

def validate_order_status_manager():
    """Validate order status manager implementation"""
    print("📊 Validating Order Status Manager...")
    
    checks = []
    
    # Check if order status manager file exists
    checks.append({
        "name": "order_status_manager.py file",
        "result": check_file_exists("src/utils/order_status_manager.py")
    })
    
    # Check OrderStatusManager class
    checks.append({
        "name": "OrderStatusManager class",
        "result": check_class_exists("src/utils/order_status_manager.py", "OrderStatusManager")
    })
    
    # Check OrderStatus enum
    checks.append({
        "name": "OrderStatus enum",
        "result": check_class_exists("src/utils/order_status_manager.py", "OrderStatus")
    })
    
    # Check DeliveryStatus enum
    checks.append({
        "name": "DeliveryStatus enum",
        "result": check_class_exists("src/utils/order_status_manager.py", "DeliveryStatus")
    })
    
    return checks

def validate_analytics_validator():
    """Validate analytics validator implementation"""
    print("💰 Validating Analytics Validator...")
    
    checks = []
    
    # Check if analytics validator file exists
    checks.append({
        "name": "analytics_validator.py file",
        "result": check_file_exists("src/utils/analytics_validator.py")
    })
    
    # Check AnalyticsValidator class
    checks.append({
        "name": "AnalyticsValidator class",
        "result": check_class_exists("src/utils/analytics_validator.py", "AnalyticsValidator")
    })
    
    # Check key methods
    if check_file_exists("src/utils/analytics_validator.py"):
        try:
            with open("src/utils/analytics_validator.py", "r", encoding="utf-8") as f:
                content = f.read()

            checks.append({
                "name": "validate_revenue_calculations method",
                "result": "def validate_revenue_calculations" in content
            })

            checks.append({
                "name": "50% earnings validation",
                "result": "Decimal('0.5')" in content
            })
        except UnicodeDecodeError:
            checks.append({
                "name": "validate_revenue_calculations method",
                "result": False
            })
            checks.append({
                "name": "50% earnings validation",
                "result": False
            })
    
    return checks

def validate_realtime_sync_manager():
    """Validate real-time sync manager implementation"""
    print("🔄 Validating Real-time Sync Manager...")
    
    checks = []
    
    # Check if realtime sync manager file exists
    checks.append({
        "name": "realtime_sync_manager.py file",
        "result": check_file_exists("src/utils/realtime_sync_manager.py")
    })
    
    # Check RealtimeSyncManager class
    checks.append({
        "name": "RealtimeSyncManager class",
        "result": check_class_exists("src/utils/realtime_sync_manager.py", "RealtimeSyncManager")
    })
    
    # Check key methods
    if check_file_exists("src/utils/realtime_sync_manager.py"):
        try:
            with open("src/utils/realtime_sync_manager.py", "r", encoding="utf-8") as f:
                content = f.read()

            checks.append({
                "name": "caching functionality",
                "result": "cache_ttl = 30" in content
            })

            checks.append({
                "name": "consistency checks",
                "result": "ensure_data_consistency" in content
            })
        except UnicodeDecodeError:
            checks.append({
                "name": "caching functionality",
                "result": False
            })
            checks.append({
                "name": "consistency checks",
                "result": False
            })
    
    return checks

def validate_data_consistency_validator():
    """Validate data consistency validator implementation"""
    print("🔍 Validating Data Consistency Validator...")
    
    checks = []
    
    # Check if data consistency validator file exists
    checks.append({
        "name": "data_consistency_validator.py file",
        "result": check_file_exists("src/utils/data_consistency_validator.py")
    })
    
    # Check DataConsistencyValidator class
    checks.append({
        "name": "DataConsistencyValidator class",
        "result": check_class_exists("src/utils/data_consistency_validator.py", "DataConsistencyValidator")
    })
    
    return checks

def validate_integration_updates():
    """Validate integration updates in existing files"""
    print("🚀 Validating Integration Updates...")
    
    checks = []
    
    # Check payment handlers integration
    if check_file_exists("src/handlers/payment_handlers.py"):
        try:
            with open("src/handlers/payment_handlers.py", "r", encoding="utf-8") as f:
                content = f.read()

            checks.append({
                "name": "payment handlers order status manager import",
                "result": "from src.utils.order_status_manager import" in content
            })

            checks.append({
                "name": "payment handlers status update usage",
                "result": "order_status_manager.update_order_status" in content
            })
        except UnicodeDecodeError:
            checks.append({
                "name": "payment handlers order status manager import",
                "result": False
            })
            checks.append({
                "name": "payment handlers status update usage",
                "result": False
            })

    # Check delivery bot integration
    if check_file_exists("src/bots/delivery_bot.py"):
        try:
            with open("src/bots/delivery_bot.py", "r", encoding="utf-8") as f:
                content = f.read()

            checks.append({
                "name": "delivery bot order status manager import",
                "result": "from src.utils.order_status_manager import" in content
            })
        except UnicodeDecodeError:
            checks.append({
                "name": "delivery bot order status manager import",
                "result": False
            })

    # Check earnings utils enhancement
    if check_file_exists("src/utils/earnings_utils.py"):
        try:
            with open("src/utils/earnings_utils.py", "r", encoding="utf-8") as f:
                content = f.read()

            checks.append({
                "name": "earnings utils 50% calculation",
                "result": "earnings_amount = delivery_fee * 0.5" in content
            })

            checks.append({
                "name": "earnings utils analytics refresh",
                "result": "refresh_analytics_data()" in content
            })
        except UnicodeDecodeError:
            checks.append({
                "name": "earnings utils 50% calculation",
                "result": False
            })
            checks.append({
                "name": "earnings utils analytics refresh",
                "result": False
            })
    
    return checks

def validate_test_files():
    """Validate test files creation"""
    print("📋 Validating Test Files...")
    
    checks = []
    
    test_files = [
        "test_complete_system_integration.py",
        "test_basic_imports.py",
        "validate_system_improvements.py"
    ]
    
    for test_file in test_files:
        checks.append({
            "name": f"{test_file} exists",
            "result": check_file_exists(test_file)
        })
    
    return checks

def main():
    """Run all validation checks"""
    print("🚀 Wiz-Aroma System Improvements Validation")
    print("=" * 60)
    
    all_checks = []
    
    # Run all validation functions
    validation_functions = [
        validate_signal_handling_improvements,
        validate_order_status_manager,
        validate_analytics_validator,
        validate_realtime_sync_manager,
        validate_data_consistency_validator,
        validate_integration_updates,
        validate_test_files
    ]
    
    for validation_func in validation_functions:
        checks = validation_func()
        all_checks.extend(checks)
        print()
    
    # Summary
    print("=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(all_checks)
    
    for check in all_checks:
        status = "✅ PASS" if check["result"] else "❌ FAIL"
        print(f"{check['name']:<40} {status}")
        if check["result"]:
            passed += 1
    
    print(f"\nTotal Checks: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed / total) * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("✅ System improvements have been successfully implemented.")
        return 0
    else:
        print(f"\n⚠️ {total - passed} validations failed.")
        print("❌ Some improvements may not be properly implemented.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
