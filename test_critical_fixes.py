#!/usr/bin/env python3
"""
Test script for critical delivery system fixes
Tests the specific issues identified in the error logs
"""

import sys
import os
import datetime
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_order_status_manager_validation():
    """Test 1: Order Status Manager Data Validation Fixes"""
    print("🔧 Test 1: Order Status Manager Data Validation")
    print("=" * 60)
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus
        from src.firebase_db import delete_data
        
        # Test with problematic order data similar to the error logs
        test_order_number = "7729984017_2507211943_0001"
        
        # Test Case 1: Order data missing user_id (like in the error logs)
        print("\n1. Testing order data missing user_id...")
        incomplete_order_data = {
            "order_number": test_order_number,
            "restaurant_name": "Test Restaurant",
            "restaurant_id": "rest_001",
            "area_id": "3",
            "subtotal": 100.0,
            "delivery_fee": 30.0,
            "total": 130.0
            # Missing user_id intentionally
        }
        
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.CONFIRMED,
            incomplete_order_data
        )
        
        if success:
            print("✅ Order status manager handled missing user_id correctly")
        else:
            print("❌ Order status manager failed with missing user_id")
            return False
        
        # Test Case 2: Order data missing restaurant_name
        print("\n2. Testing order data missing restaurant_name...")
        incomplete_order_data_2 = {
            "order_number": test_order_number + "_2",
            "user_id": "7729984017",
            "restaurant_id": "rest_002",
            "area_id": "3",
            "subtotal": 150.0,
            "delivery_fee": 35.0,
            "total": 185.0
            # Missing restaurant_name intentionally
        }
        
        success = order_status_manager.update_order_status(
            test_order_number + "_2",
            OrderStatus.CONFIRMED,
            incomplete_order_data_2
        )
        
        if success:
            print("✅ Order status manager handled missing restaurant_name correctly")
        else:
            print("❌ Order status manager failed with missing restaurant_name")
            return False
        
        # Clean up test data
        delete_data(f"confirmed_orders/{test_order_number}")
        delete_data(f"confirmed_orders/{test_order_number}_2")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order status manager: {e}")
        traceback.print_exc()
        return False

def test_analytics_decimal_conversion():
    """Test 2: Analytics Validator Decimal Conversion Fixes"""
    print("\n💰 Test 2: Analytics Validator Decimal Conversion")
    print("=" * 60)
    
    try:
        from src.utils.analytics_validator import analytics_validator
        from src.firebase_db import set_data, delete_data
        import datetime
        
        # Create test order data with integer values (like in the error)
        test_orders = {
            "test_order_1": {
                "order_number": "test_order_1",
                "subtotal": 100,  # Integer value
                "delivery_fee": 30,  # Integer value
                "completed_at": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            "test_order_2": {
                "order_number": "test_order_2",
                "subtotal": 150.5,  # Float value
                "delivery_fee": 35.0,  # Float value
                "completed_at": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        # Store test orders
        set_data("completed_orders", test_orders)
        
        print("1. Testing revenue calculations with mixed data types...")
        validation_results = analytics_validator.validate_revenue_calculations()
        
        if validation_results and "revenue_validation" in validation_results:
            weekly_validation = validation_results["revenue_validation"].get("weekly", {})
            monthly_validation = validation_results["revenue_validation"].get("monthly", {})
            
            if weekly_validation.get("validation_status") == "passed":
                print("✅ Weekly revenue validation passed")
            else:
                print(f"❌ Weekly revenue validation failed: {weekly_validation}")
                return False
            
            if monthly_validation.get("validation_status") == "passed":
                print("✅ Monthly revenue validation passed")
            else:
                print(f"❌ Monthly revenue validation failed: {monthly_validation}")
                return False
        else:
            print("❌ Analytics validation failed to complete")
            return False
        
        # Clean up test data
        delete_data("completed_orders")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing analytics validator: {e}")
        traceback.print_exc()
        return False

def test_delivery_broadcast_workflow():
    """Test 3: Delivery Broadcast Workflow"""
    print("\n📡 Test 3: Delivery Broadcast Workflow")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        from src.firebase_db import get_data
        
        # Test personnel availability for area 3 (from error logs)
        print("1. Testing personnel availability for area 3...")
        available_personnel = find_available_personnel_with_capacity_check("3")
        print(f"   Available personnel: {len(available_personnel)} - {available_personnel}")
        
        if len(available_personnel) == 0:
            print("⚠️ No available personnel found for area 3")
            return False
        
        # Test personnel data completeness
        print("\n2. Testing personnel data completeness...")
        personnel_data = get_data("delivery_personnel") or {}
        
        broadcast_ready_count = 0
        for personnel_id in available_personnel:
            pdata = personnel_data.get(personnel_id)
            if pdata:
                telegram_id = pdata.get('telegram_id')
                name = pdata.get('name', 'Unknown')
                
                if telegram_id:
                    broadcast_ready_count += 1
                    print(f"   ✅ {personnel_id} ({name}): Ready - TG: {telegram_id}")
                else:
                    print(f"   ❌ {personnel_id} ({name}): Missing telegram_id")
            else:
                print(f"   ❌ {personnel_id}: No personnel data found")
        
        print(f"\n   Total broadcast-ready personnel: {broadcast_ready_count}")
        
        if broadcast_ready_count > 0:
            print("✅ Personnel are ready for broadcast")
            return True
        else:
            print("❌ No personnel ready for broadcast")
            return False
        
    except Exception as e:
        print(f"❌ Error testing delivery broadcast: {e}")
        traceback.print_exc()
        return False

def test_complete_order_workflow():
    """Test 4: Complete Order Workflow from Payment to Broadcast"""
    print("\n🔄 Test 4: Complete Order Workflow")
    print("=" * 60)
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus
        from src.firebase_db import get_data, delete_data
        
        # Simulate the exact order from error logs
        test_order_number = "7729984017_2507211943_TEST"
        user_id = "7729984017"
        
        # Create order data similar to payment handler output
        order_data = {
            "order_number": test_order_number,
            "user_id": user_id,
            "customer_name": "Test Customer",
            "restaurant_name": "Test Restaurant",
            "restaurant_id": "rest_003",
            "restaurant_area_id": "3",
            "area_id": "3",
            "delivery_location_id": "loc_001",
            "delivery_address": "Test Address",
            "subtotal": 100.0,
            "delivery_fee": 30.0,
            "total": 130.0,
            "payment_method": "telebirr",
            "payment_status": "confirmed"
        }
        
        print(f"1. Testing order confirmation for {test_order_number}...")
        
        # Test order status update to CONFIRMED
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.CONFIRMED,
            order_data
        )
        
        if not success:
            print("❌ Order confirmation failed")
            return False
        
        print("✅ Order confirmed successfully")
        
        # Verify order is in confirmed_orders collection
        print("\n2. Verifying order persistence...")
        confirmed_order = get_data(f"confirmed_orders/{test_order_number}")
        
        if not confirmed_order:
            print("❌ Order not found in confirmed_orders collection")
            return False
        
        print("✅ Order found in confirmed_orders collection")
        print(f"   Status: {confirmed_order.get('status')}")
        print(f"   Delivery Status: {confirmed_order.get('delivery_status')}")
        print(f"   User ID: {confirmed_order.get('user_id')}")
        print(f"   Restaurant Name: {confirmed_order.get('restaurant_name')}")
        
        # Test delivery personnel availability
        print("\n3. Testing delivery personnel availability...")
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        
        delivery_area_id = confirmed_order.get('restaurant_area_id') or confirmed_order.get('area_id')
        available_personnel = find_available_personnel_with_capacity_check(str(delivery_area_id))
        
        print(f"   Delivery area: {delivery_area_id}")
        print(f"   Available personnel: {len(available_personnel)} - {available_personnel}")
        
        if len(available_personnel) > 0:
            print("✅ Personnel available for broadcast")
        else:
            print("⚠️ No personnel available for broadcast")
        
        # Clean up
        delete_data(f"confirmed_orders/{test_order_number}")
        print("\n🧹 Test cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing complete workflow: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all critical fix tests"""
    print("🚀 Critical Delivery System Fixes - Validation Tests")
    print("=" * 80)
    
    tests = [
        ("Order Status Manager Validation", test_order_status_manager_validation),
        ("Analytics Decimal Conversion", test_analytics_decimal_conversion),
        ("Delivery Broadcast Workflow", test_delivery_broadcast_workflow),
        ("Complete Order Workflow", test_complete_order_workflow),
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_function in tests:
        print(f"\n{'='*80}")
        try:
            result = test_function()
            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - ERROR: {e}")
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 CRITICAL FIXES VALIDATION SUMMARY")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
    
    total_tests = len(tests)
    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL CRITICAL FIXES VALIDATED SUCCESSFULLY!")
        print("✅ The delivery system should now process orders correctly.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} tests failed.")
        print("❌ Additional fixes may be needed.")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
