#!/usr/bin/env python3
"""
Basic import test to verify all modules are working
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic imports"""
    print("🔧 Testing Basic Imports...")
    
    try:
        # Test main module imports
        print("  ✅ Testing main module imports...")
        from main import interruptible_sleep, shutdown_event
        print("    ✅ Main module imports successful")
        
        # Test config imports
        print("  ✅ Testing config imports...")
        from src.config import logger
        print("    ✅ Config imports successful")
        
        # Test Firebase imports
        print("  ✅ Testing Firebase imports...")
        from src.firebase_db import get_data, set_data
        print("    ✅ Firebase imports successful")
        
        # Test utility imports
        print("  ✅ Testing utility imports...")
        from src.utils.order_status_manager import order_status_manager
        print("    ✅ Order status manager import successful")
        
        from src.utils.analytics_validator import analytics_validator
        print("    ✅ Analytics validator import successful")
        
        from src.utils.data_consistency_validator import data_consistency_validator
        print("    ✅ Data consistency validator import successful")
        
        from src.utils.realtime_sync_manager import realtime_sync_manager
        print("    ✅ Real-time sync manager import successful")
        
        from src.utils.earnings_utils import update_personnel_earnings
        print("    ✅ Earnings utils import successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_handling():
    """Test signal handling functionality"""
    print("🔧 Testing Signal Handling...")
    
    try:
        from main import interruptible_sleep, shutdown_event
        import time
        
        # Test interruptible sleep
        print("  ✅ Testing interruptible sleep...")
        start_time = time.time()
        result = interruptible_sleep(1)  # 1 second sleep
        elapsed = time.time() - start_time
        
        if result and 0.8 <= elapsed <= 1.2:
            print(f"    ✅ Interruptible sleep working ({elapsed:.1f}s)")
            return True
        else:
            print(f"    ⚠️ Interruptible sleep timing issue ({elapsed:.1f}s)")
            return False
        
    except Exception as e:
        print(f"  ❌ Signal handling error: {e}")
        return False

def main():
    """Run basic tests"""
    print("🚀 Wiz Aroma - Basic System Test")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Signal Handling", test_signal_handling),
    ]
    
    results = {}
    for test_name, test_function in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            result = test_function()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status} {test_name}")
        except Exception as e:
            results[test_name] = False
            print(f"❌ FAILED {test_name}: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        return 0
    else:
        print("⚠️ Some tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
