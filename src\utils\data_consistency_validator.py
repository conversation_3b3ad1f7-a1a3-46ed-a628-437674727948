"""
Data Consistency Validator for Wiz Aroma Delivery System
Validates data integrity across Firebase collections and ensures synchronization
"""

import datetime
import logging
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict

from src.firebase_db import get_data, set_data, delete_data
from src.config import logger


class DataConsistencyValidator:
    """Validates and maintains data consistency across Firebase collections"""
    
    def __init__(self):
        self.logger = logger
        self.validation_errors = []
        self.fixed_issues = []
    
    def validate_order_lifecycle_consistency(self) -> Dict[str, Any]:
        """Validate consistency across all order-related collections"""
        try:
            self.logger.info("Starting order lifecycle consistency validation...")
            
            # Get all order data from different collections
            pending_orders = get_data("pending_admin_reviews") or {}
            confirmed_orders = get_data("confirmed_orders") or {}
            completed_orders = get_data("completed_orders") or {}
            awaiting_receipt = get_data("awaiting_receipt") or {}
            
            # Get delivery assignments
            assignments = get_data("delivery_personnel_assignments") or {}
            
            # Get earnings data
            earnings_data = get_data("delivery_personnel_earnings") or {}
            
            validation_results = {
                "total_orders_checked": 0,
                "consistency_errors": [],
                "fixed_issues": [],
                "collections_summary": {
                    "pending_admin_reviews": len(pending_orders),
                    "confirmed_orders": len(confirmed_orders),
                    "completed_orders": len(completed_orders),
                    "awaiting_receipt": len(awaiting_receipt),
                    "delivery_assignments": len(assignments)
                }
            }
            
            # Validate each collection
            all_orders = {}
            all_orders.update({f"pending_{k}": v for k, v in pending_orders.items()})
            all_orders.update({f"confirmed_{k}": v for k, v in confirmed_orders.items()})
            all_orders.update({f"completed_{k}": v for k, v in completed_orders.items()})
            all_orders.update({f"awaiting_{k}": v for k, v in awaiting_receipt.items()})
            
            validation_results["total_orders_checked"] = len(all_orders)
            
            # Check for duplicate orders across collections
            order_numbers = defaultdict(list)
            for collection_order_id, order_data in all_orders.items():
                if isinstance(order_data, dict):
                    order_number = order_data.get('order_number')
                    if order_number:
                        order_numbers[order_number].append(collection_order_id)
            
            # Find duplicates
            for order_number, locations in order_numbers.items():
                if len(locations) > 1:
                    error = {
                        "type": "duplicate_order",
                        "order_number": order_number,
                        "locations": locations,
                        "severity": "high"
                    }
                    validation_results["consistency_errors"].append(error)
                    self.logger.warning(f"Duplicate order found: {order_number} in {locations}")
            
            # Validate order status consistency
            for order_number, locations in order_numbers.items():
                if len(locations) == 1:  # Single location orders
                    location = locations[0]
                    order_data = all_orders[location]
                    
                    # Validate status fields
                    status_errors = self._validate_order_status_fields(order_number, order_data)
                    validation_results["consistency_errors"].extend(status_errors)
                    
                    # Validate delivery assignment consistency
                    assignment_errors = self._validate_delivery_assignment_consistency(
                        order_number, order_data, assignments
                    )
                    validation_results["consistency_errors"].extend(assignment_errors)
                    
                    # Validate earnings consistency for completed orders
                    if order_data.get('delivery_status') == 'completed':
                        earnings_errors = self._validate_earnings_consistency(
                            order_number, order_data, assignments, earnings_data
                        )
                        validation_results["consistency_errors"].extend(earnings_errors)
            
            # Validate orphaned assignments
            orphaned_assignments = self._find_orphaned_assignments(assignments, all_orders)
            for assignment_id, assignment_data in orphaned_assignments.items():
                error = {
                    "type": "orphaned_assignment",
                    "assignment_id": assignment_id,
                    "order_number": assignment_data.get('order_number'),
                    "severity": "medium"
                }
                validation_results["consistency_errors"].append(error)
            
            self.logger.info(f"Validation completed. Found {len(validation_results['consistency_errors'])} issues")
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Error during consistency validation: {e}")
            return {"error": str(e)}
    
    def _validate_order_status_fields(self, order_number: str, order_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate order status fields consistency"""
        errors = []
        
        try:
            status = order_data.get('status')
            delivery_status = order_data.get('delivery_status')
            
            # Check for missing required fields
            if not status:
                errors.append({
                    "type": "missing_status",
                    "order_number": order_number,
                    "field": "status",
                    "severity": "high"
                })
            
            if not delivery_status and status in ['CONFIRMED', 'APPROVED']:
                errors.append({
                    "type": "missing_delivery_status",
                    "order_number": order_number,
                    "field": "delivery_status",
                    "severity": "medium"
                })
            
            # Check status consistency
            if status == 'CONFIRMED' and delivery_status not in ['pending_assignment', 'assigned', 'completed', 'customer_confirmed']:
                errors.append({
                    "type": "inconsistent_status",
                    "order_number": order_number,
                    "status": status,
                    "delivery_status": delivery_status,
                    "severity": "high"
                })
            
            # Check timestamp consistency
            if delivery_status == 'completed' and not order_data.get('completed_at'):
                errors.append({
                    "type": "missing_timestamp",
                    "order_number": order_number,
                    "field": "completed_at",
                    "severity": "medium"
                })
            
        except Exception as e:
            self.logger.error(f"Error validating status fields for {order_number}: {e}")
        
        return errors
    
    def _validate_delivery_assignment_consistency(self, order_number: str, order_data: Dict[str, Any], 
                                                assignments: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate delivery assignment consistency"""
        errors = []
        
        try:
            assigned_to = order_data.get('assigned_to')
            delivery_status = order_data.get('delivery_status')
            
            # Find assignments for this order
            order_assignments = [
                (aid, adata) for aid, adata in assignments.items()
                if adata.get('order_number') == order_number
            ]
            
            # Check assignment consistency
            if delivery_status in ['assigned', 'completed'] and not assigned_to:
                errors.append({
                    "type": "missing_assignment",
                    "order_number": order_number,
                    "delivery_status": delivery_status,
                    "severity": "high"
                })
            
            if assigned_to and not order_assignments:
                errors.append({
                    "type": "missing_assignment_record",
                    "order_number": order_number,
                    "assigned_to": assigned_to,
                    "severity": "high"
                })
            
            # Check for multiple active assignments
            active_assignments = [
                (aid, adata) for aid, adata in order_assignments
                if adata.get('status') in ['assigned', 'picked_up', 'in_transit']
            ]
            
            if len(active_assignments) > 1:
                errors.append({
                    "type": "multiple_active_assignments",
                    "order_number": order_number,
                    "assignments": [aid for aid, _ in active_assignments],
                    "severity": "high"
                })
            
        except Exception as e:
            self.logger.error(f"Error validating assignment consistency for {order_number}: {e}")
        
        return errors
    
    def _validate_earnings_consistency(self, order_number: str, order_data: Dict[str, Any],
                                     assignments: Dict[str, Any], earnings_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate earnings consistency for completed orders"""
        errors = []
        
        try:
            assigned_to = order_data.get('assigned_to')
            delivery_fee = order_data.get('delivery_fee', 0)
            
            if not assigned_to or delivery_fee <= 0:
                return errors
            
            # Check if earnings were recorded
            personnel_earnings = earnings_data.get(assigned_to)
            if not personnel_earnings:
                errors.append({
                    "type": "missing_earnings_record",
                    "order_number": order_number,
                    "personnel_id": assigned_to,
                    "delivery_fee": delivery_fee,
                    "severity": "medium"
                })
            
        except Exception as e:
            self.logger.error(f"Error validating earnings consistency for {order_number}: {e}")
        
        return errors
    
    def _find_orphaned_assignments(self, assignments: Dict[str, Any], all_orders: Dict[str, Any]) -> Dict[str, Any]:
        """Find delivery assignments without corresponding orders"""
        orphaned = {}
        
        try:
            # Get all order numbers from orders
            order_numbers = set()
            for order_data in all_orders.values():
                if isinstance(order_data, dict):
                    order_number = order_data.get('order_number')
                    if order_number:
                        order_numbers.add(order_number)
            
            # Check assignments
            for assignment_id, assignment_data in assignments.items():
                if isinstance(assignment_data, dict):
                    assignment_order_number = assignment_data.get('order_number')
                    if assignment_order_number and assignment_order_number not in order_numbers:
                        orphaned[assignment_id] = assignment_data
            
        except Exception as e:
            self.logger.error(f"Error finding orphaned assignments: {e}")
        
        return orphaned
    
    def fix_consistency_issues(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Attempt to fix consistency issues automatically"""
        fixed_count = 0
        fix_results = {"fixed": [], "failed": []}
        
        try:
            for error in validation_results.get("consistency_errors", []):
                error_type = error.get("type")
                
                if error_type == "missing_delivery_status":
                    # Fix missing delivery status
                    if self._fix_missing_delivery_status(error):
                        fixed_count += 1
                        fix_results["fixed"].append(error)
                    else:
                        fix_results["failed"].append(error)
                
                elif error_type == "missing_timestamp":
                    # Fix missing timestamps
                    if self._fix_missing_timestamp(error):
                        fixed_count += 1
                        fix_results["fixed"].append(error)
                    else:
                        fix_results["failed"].append(error)
            
            self.logger.info(f"Fixed {fixed_count} consistency issues")
            return fix_results
            
        except Exception as e:
            self.logger.error(f"Error fixing consistency issues: {e}")
            return {"error": str(e)}
    
    def _fix_missing_delivery_status(self, error: Dict[str, Any]) -> bool:
        """Fix missing delivery status"""
        try:
            order_number = error.get("order_number")
            order_data = self._get_order_from_any_collection(order_number)
            
            if order_data:
                # Set default delivery status based on order status
                status = order_data.get('status')
                if status == 'CONFIRMED':
                    order_data['delivery_status'] = 'pending_assignment'
                elif status == 'APPROVED':
                    order_data['delivery_status'] = 'pending_assignment'
                
                # Update in Firebase
                collection_path = self._determine_collection_path(order_data)
                return set_data(f"{collection_path}/{order_number}", order_data)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error fixing missing delivery status: {e}")
            return False
    
    def _fix_missing_timestamp(self, error: Dict[str, Any]) -> bool:
        """Fix missing timestamp"""
        try:
            order_number = error.get("order_number")
            field = error.get("field")
            
            order_data = self._get_order_from_any_collection(order_number)
            if order_data and field:
                # Set current timestamp
                order_data[field] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # Update in Firebase
                collection_path = self._determine_collection_path(order_data)
                return set_data(f"{collection_path}/{order_number}", order_data)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error fixing missing timestamp: {e}")
            return False
    
    def _get_order_from_any_collection(self, order_number: str) -> Optional[Dict[str, Any]]:
        """Get order data from any collection"""
        collections = ["confirmed_orders", "completed_orders", "pending_admin_reviews", "awaiting_receipt"]
        
        for collection in collections:
            order_data = get_data(f"{collection}/{order_number}")
            if order_data:
                return order_data
        
        return None
    
    def _determine_collection_path(self, order_data: Dict[str, Any]) -> str:
        """Determine which collection the order should be in"""
        status = order_data.get('status')
        
        if status == 'PENDING_REVIEW':
            return "pending_admin_reviews"
        elif status in ['CONFIRMED', 'APPROVED']:
            return "confirmed_orders"
        elif status == 'CUSTOMER_CONFIRMED':
            return "completed_orders"
        else:
            return "confirmed_orders"  # Default


# Global instance
data_consistency_validator = DataConsistencyValidator()
