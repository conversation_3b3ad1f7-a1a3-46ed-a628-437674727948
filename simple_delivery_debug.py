#!/usr/bin/env python3
"""
Simple delivery notification debugger
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def check_basic_imports():
    """Check if basic imports work"""
    print("🔍 Checking basic imports...")
    
    try:
        from src.firebase_db import get_data, set_data
        print("✅ Firebase imports successful")
        
        from src.config import DELIVERY_BOT_TOKEN
        print(f"✅ Delivery bot token configured: {DELIVERY_BOT_TOKEN[:20]}...")
        
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        print("✅ Personnel utils imports successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def check_firebase_collections():
    """Check Firebase collections"""
    print("\n🔍 Checking Firebase collections...")
    
    try:
        from src.firebase_db import get_data
        
        # Check confirmed_orders
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"📦 Confirmed orders: {len(confirmed_orders)}")
        
        # Check delivery_personnel
        delivery_personnel = get_data("delivery_personnel") or {}
        print(f"👥 Delivery personnel: {len(delivery_personnel)}")
        
        # Check authorized_delivery_personnel
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"🔐 Authorized personnel: {len(authorized_personnel)}")
        
        return True
    except Exception as e:
        print(f"❌ Firebase error: {e}")
        return False

def check_personnel_availability():
    """Check personnel availability"""
    print("\n🔍 Checking personnel availability...")
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        
        # Test area 1
        available = find_available_personnel_with_capacity_check("1")
        print(f"📍 Area 1 available personnel: {len(available)} - {available}")
        
        return True
    except Exception as e:
        print(f"❌ Personnel availability error: {e}")
        return False

def main():
    """Run simple diagnostics"""
    print("🚀 Simple Delivery Notification Diagnostics")
    print("=" * 50)
    
    tests = [
        check_basic_imports,
        check_firebase_collections,
        check_personnel_availability
    ]
    
    for test in tests:
        try:
            result = test()
            if not result:
                print("❌ Test failed")
                return 1
        except Exception as e:
            print(f"❌ Test error: {e}")
            return 1
    
    print("\n✅ All basic checks passed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
