"""
Order Status Manager for Wiz Aroma Delivery System
Handles order lifecycle tracking and Firebase synchronization
"""

import datetime
import logging
from typing import Dict, Any, Optional, List
from enum import Enum

from src.firebase_db import get_data, set_data, delete_data, update_data
from src.config import logger


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    CONFIRMED = "confirmed"
    PENDING_ASSIGNMENT = "pending_assignment"
    ASSIGNED = "assigned"
    PICKED_UP = "picked_up"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CUSTOMER_CONFIRMED = "customer_confirmed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class DeliveryStatus(Enum):
    """Delivery status enumeration"""
    PENDING_ASSIGNMENT = "pending_assignment"
    ASSIGNED = "assigned"
    ACCEPTED = "accepted"
    PICKED_UP = "picked_up"
    IN_TRANSIT = "in_transit"
    COMPLETED = "completed"
    CUSTOMER_CONFIRMED = "customer_confirmed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class OrderStatusManager:
    """Manages order status transitions and Firebase synchronization"""
    
    def __init__(self):
        self.logger = logger
        
    def validate_order_data(self, order_data: Dict[str, Any]) -> bool:
        """Validate order data structure"""
        required_fields = ['order_number', 'user_id', 'restaurant_name']
        for field in required_fields:
            if field not in order_data:
                self.logger.error(f"Missing required field in order data: {field}")
                return False
        return True
    
    def update_order_status(self, order_number: str, new_status: OrderStatus,
                          additional_data: Optional[Dict[str, Any]] = None) -> bool:
        """Update order status with Firebase synchronization"""
        try:
            # Get current order data or create new if not found
            order_data = self.get_order_data(order_number)
            if not order_data:
                # If order not found and we have additional_data, create new order
                if additional_data and self.validate_order_data(additional_data):
                    order_data = additional_data.copy()
                    self.logger.info(f"Creating new order data for {order_number}")
                else:
                    self.logger.error(f"Order not found and insufficient data to create: {order_number}")
                    return False

            # Validate status transition
            current_status = order_data.get('status')
            if not self._validate_status_transition(current_status, new_status.value):
                self.logger.warning(f"Invalid status transition for order {order_number}: "
                                  f"{current_status} -> {new_status.value}")
                # Allow transition anyway for system recovery

            # Update order data
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            order_data['status'] = new_status.value
            order_data['last_updated'] = timestamp
            order_data[f'{new_status.value}_at'] = timestamp

            # Ensure order_number is set
            order_data['order_number'] = order_number

            # Add additional data if provided
            if additional_data:
                order_data.update(additional_data)

            # Determine which Firebase collection to use
            collection_path = self._get_collection_path(new_status)

            # Clean up old collections BEFORE updating new one
            self._cleanup_old_collections(order_number, new_status)

            # Update in Firebase using real-time sync manager
            try:
                from src.utils.realtime_sync_manager import realtime_sync_manager
                success = realtime_sync_manager.update_order_data(order_number, order_data, collection_path)
            except ImportError:
                # Fallback to direct Firebase update
                success = set_data(f"{collection_path}/{order_number}", order_data)

            if success:
                self.logger.info(f"Updated order {order_number} status to {new_status.value} in {collection_path}")

                # Update analytics if order is completed
                if new_status in [OrderStatus.CUSTOMER_CONFIRMED, OrderStatus.COMPLETED]:
                    self._update_analytics(order_data)

                # Ensure data consistency
                try:
                    from src.utils.realtime_sync_manager import realtime_sync_manager
                    consistency_report = realtime_sync_manager.ensure_data_consistency(order_number)
                    if consistency_report.get("status") != "consistent":
                        self.logger.warning(f"Data consistency issues found for order {order_number}: {consistency_report}")
                except ImportError:
                    pass
            else:
                self.logger.error(f"Failed to update order {order_number} in Firebase collection {collection_path}")

            return success

        except Exception as e:
            self.logger.error(f"Error updating order status for {order_number}: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False
    
    def update_delivery_status(self, order_number: str, new_delivery_status: DeliveryStatus,
                             personnel_id: Optional[str] = None) -> bool:
        """Update delivery status with Firebase synchronization"""
        try:
            # Get current order data
            order_data = self.get_order_data(order_number)
            if not order_data:
                self.logger.error(f"Order not found for delivery status update: {order_number}")
                return False
            
            # Update delivery status
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            order_data['delivery_status'] = new_delivery_status.value
            order_data['delivery_last_updated'] = timestamp
            order_data[f'delivery_{new_delivery_status.value}_at'] = timestamp
            
            # Add personnel info if provided
            if personnel_id:
                order_data['assigned_to'] = personnel_id
                if new_delivery_status == DeliveryStatus.ASSIGNED:
                    order_data['assigned_at'] = timestamp
                elif new_delivery_status == DeliveryStatus.COMPLETED:
                    order_data['completed_by'] = personnel_id
                    order_data['completed_at'] = timestamp
            
            # Update in Firebase (confirmed_orders collection for active orders)
            success = set_data(f"confirmed_orders/{order_number}", order_data)
            
            if success:
                self.logger.info(f"Updated order {order_number} delivery status to {new_delivery_status.value}")
                
                # Update delivery personnel earnings if completed
                if new_delivery_status == DeliveryStatus.COMPLETED and personnel_id:
                    self._update_personnel_earnings(personnel_id, order_data.get('delivery_fee', 0))
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating delivery status for {order_number}: {e}")
            return False
    
    def get_order_data(self, order_number: str) -> Optional[Dict[str, Any]]:
        """Get order data from appropriate Firebase collection with caching"""
        try:
            # Use real-time sync manager for cached access
            from src.utils.realtime_sync_manager import realtime_sync_manager
            return realtime_sync_manager.get_order_data(order_number)
        except ImportError:
            # Fallback to direct Firebase access
            # Check confirmed_orders first (active orders)
            order_data = get_data(f"confirmed_orders/{order_number}")
            if order_data:
                return order_data

            # Check completed_orders
            order_data = get_data(f"completed_orders/{order_number}")
            if order_data:
                return order_data

            # Check pending_admin_reviews
            order_data = get_data(f"pending_admin_reviews/{order_number}")
            if order_data:
                return order_data

            # Check awaiting_receipt
            order_data = get_data(f"awaiting_receipt/{order_number}")
            if order_data:
                return order_data

            return None
    
    def move_order_to_completed(self, order_number: str) -> bool:
        """Move order from confirmed_orders to completed_orders"""
        try:
            # Get order data from confirmed_orders
            order_data = get_data(f"confirmed_orders/{order_number}")
            if not order_data:
                self.logger.error(f"Order not found in confirmed_orders: {order_number}")
                return False
            
            # Add completion timestamp
            order_data['final_completed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            order_data['final_status'] = 'CUSTOMER_CONFIRMED'
            
            # Store in completed_orders
            success = set_data(f"completed_orders/{order_number}", order_data)
            
            if success:
                # Remove from confirmed_orders
                delete_success = delete_data(f"confirmed_orders/{order_number}")
                if delete_success:
                    self.logger.info(f"Successfully moved order {order_number} to completed_orders")
                    
                    # Update analytics
                    self._update_analytics(order_data)
                    
                    return True
                else:
                    self.logger.error(f"Failed to remove order {order_number} from confirmed_orders")
                    return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error moving order to completed: {order_number}: {e}")
            return False
    
    def _validate_status_transition(self, current_status: str, new_status: str) -> bool:
        """Validate if status transition is allowed"""
        # Define valid transitions
        valid_transitions = {
            None: [OrderStatus.PENDING_REVIEW.value],
            OrderStatus.PENDING_REVIEW.value: [OrderStatus.APPROVED.value, OrderStatus.CANCELLED.value],
            OrderStatus.APPROVED.value: [OrderStatus.CONFIRMED.value, OrderStatus.CANCELLED.value],
            OrderStatus.CONFIRMED.value: [OrderStatus.PENDING_ASSIGNMENT.value, OrderStatus.CANCELLED.value],
            OrderStatus.PENDING_ASSIGNMENT.value: [OrderStatus.ASSIGNED.value, OrderStatus.CANCELLED.value],
            OrderStatus.ASSIGNED.value: [OrderStatus.DELIVERED.value, OrderStatus.CANCELLED.value],
            OrderStatus.DELIVERED.value: [OrderStatus.CUSTOMER_CONFIRMED.value, OrderStatus.FAILED.value],
            OrderStatus.CUSTOMER_CONFIRMED.value: [],  # Final state
            OrderStatus.CANCELLED.value: [],  # Final state
            OrderStatus.FAILED.value: [OrderStatus.PENDING_ASSIGNMENT.value]  # Can retry
        }
        
        allowed_transitions = valid_transitions.get(current_status, [])
        return new_status in allowed_transitions
    
    def _get_collection_path(self, status: OrderStatus) -> str:
        """Get Firebase collection path based on order status"""
        if status in [OrderStatus.PENDING_REVIEW]:
            return "pending_admin_reviews"
        elif status in [OrderStatus.CONFIRMED, OrderStatus.PENDING_ASSIGNMENT, 
                       OrderStatus.ASSIGNED, OrderStatus.DELIVERED]:
            return "confirmed_orders"
        elif status in [OrderStatus.CUSTOMER_CONFIRMED, OrderStatus.COMPLETED]:
            return "completed_orders"
        else:
            return "confirmed_orders"  # Default
    
    def _cleanup_old_collections(self, order_number: str, new_status: OrderStatus):
        """Clean up order from old collections when status changes"""
        try:
            # Define cleanup rules based on status transitions
            if new_status == OrderStatus.APPROVED:
                # Order approved, might still be in pending_admin_reviews
                pass  # Keep in pending until confirmed
            elif new_status == OrderStatus.CONFIRMED:
                # Remove from pending_admin_reviews when confirmed
                delete_success = delete_data(f"pending_admin_reviews/{order_number}")
                if delete_success:
                    self.logger.info(f"Removed order {order_number} from pending_admin_reviews")
                else:
                    self.logger.warning(f"Failed to remove order {order_number} from pending_admin_reviews")
            elif new_status == OrderStatus.CUSTOMER_CONFIRMED:
                # Remove from confirmed_orders (handled by move_order_to_completed)
                pass

            # Additional cleanup for any orphaned entries
            collections_to_check = ["pending_admin_reviews", "confirmed_orders", "awaiting_receipt"]
            target_collection = self._get_collection_path(new_status)

            for collection in collections_to_check:
                if collection != target_collection:
                    # Check if order exists in this collection and remove if found
                    existing_data = get_data(f"{collection}/{order_number}")
                    if existing_data:
                        delete_success = delete_data(f"{collection}/{order_number}")
                        if delete_success:
                            self.logger.info(f"Cleaned up orphaned order {order_number} from {collection}")
                        else:
                            self.logger.warning(f"Failed to clean up orphaned order {order_number} from {collection}")

        except Exception as e:
            self.logger.error(f"Error cleaning up old collections for {order_number}: {e}")
            import traceback
            self.logger.error(f"Cleanup traceback: {traceback.format_exc()}")
    
    def _update_analytics(self, order_data: Dict[str, Any]):
        """Update analytics data when order is completed"""
        try:
            # This will be called by the analytics system
            # For now, just log the completion
            self.logger.info(f"Order completed for analytics: {order_data.get('order_number')}")
            
        except Exception as e:
            self.logger.error(f"Error updating analytics: {e}")
    
    def _update_personnel_earnings(self, personnel_id: str, delivery_fee: float):
        """Update delivery personnel earnings"""
        try:
            if delivery_fee > 0:
                from src.utils.earnings_utils import update_personnel_earnings
                success = update_personnel_earnings(personnel_id, delivery_fee)
                if success:
                    self.logger.info(f"Updated earnings for personnel {personnel_id}: +{delivery_fee} birr")
                else:
                    self.logger.error(f"Failed to update earnings for personnel {personnel_id}")
                    
        except Exception as e:
            self.logger.error(f"Error updating personnel earnings: {e}")


# Global instance
order_status_manager = OrderStatusManager()
