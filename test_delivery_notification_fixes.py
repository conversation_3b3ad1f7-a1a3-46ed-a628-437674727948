#!/usr/bin/env python3
"""
Test script to validate delivery notification system fixes
"""

import sys
import os
import datetime
import uuid

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_order_status_manager_fixes():
    """Test the order status manager improvements"""
    print("🔧 Testing Order Status Manager Fixes")
    print("=" * 50)
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus
        from src.firebase_db import get_data, delete_data
        
        # Create test order data
        test_order_number = f"TEST_OSM_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_order_data = {
            "order_number": test_order_number,
            "user_id": "test_user_123",
            "restaurant_name": "Test Restaurant",
            "customer_name": "Test Customer",
            "restaurant_id": "rest_001",
            "area_id": "1",
            "delivery_location_id": "loc_001",
            "subtotal": 100.0,
            "delivery_fee": 30.0,
            "total": 130.0
        }
        
        print(f"📦 Testing with order: {test_order_number}")
        
        # Test 1: Update to CONFIRMED status with complete data
        print("\n1. Testing CONFIRMED status update...")
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.CONFIRMED,
            test_order_data
        )
        
        if success:
            print("✅ Order status update to CONFIRMED successful")
            
            # Verify order is in confirmed_orders collection
            confirmed_order = get_data(f"confirmed_orders/{test_order_number}")
            if confirmed_order:
                print("✅ Order found in confirmed_orders collection")
                print(f"   Status: {confirmed_order.get('status')}")
                print(f"   Order Number: {confirmed_order.get('order_number')}")
                print(f"   Delivery Status: {confirmed_order.get('delivery_status')}")
            else:
                print("❌ Order NOT found in confirmed_orders collection")
                return False
        else:
            print("❌ Order status update failed")
            return False
        
        # Test 2: Verify cleanup of old collections
        print("\n2. Testing collection cleanup...")
        pending_order = get_data(f"pending_admin_reviews/{test_order_number}")
        if not pending_order:
            print("✅ Order properly removed from pending_admin_reviews")
        else:
            print("⚠️ Order still exists in pending_admin_reviews")
        
        # Clean up
        delete_data(f"confirmed_orders/{test_order_number}")
        print("🧹 Test cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order status manager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_personnel_availability():
    """Test delivery personnel availability system"""
    print("\n👥 Testing Delivery Personnel Availability")
    print("=" * 50)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        from src.firebase_db import get_data
        
        # Check delivery personnel data
        delivery_personnel = get_data("delivery_personnel") or {}
        print(f"📊 Total delivery personnel in database: {len(delivery_personnel)}")
        
        if delivery_personnel:
            print("\nPersonnel status:")
            for pid, pdata in delivery_personnel.items():
                name = pdata.get('name', 'Unknown')
                status = pdata.get('status', 'Unknown')
                verified = pdata.get('is_verified', False)
                capacity = pdata.get('current_capacity', 0)
                max_cap = pdata.get('max_capacity', 5)
                areas = pdata.get('service_areas', [])
                telegram_id = pdata.get('telegram_id', 'None')
                
                print(f"  {pid}:")
                print(f"    Name: {name}")
                print(f"    Status: {status}")
                print(f"    Verified: {verified}")
                print(f"    Capacity: {capacity}/{max_cap}")
                print(f"    Areas: {areas}")
                print(f"    Telegram ID: {telegram_id}")
                print()
        
        # Test availability for different areas
        test_areas = ['1', '2', '3', '4', '5']
        
        for area_id in test_areas:
            print(f"🔍 Testing area {area_id}:")
            available_personnel = find_available_personnel_with_capacity_check(area_id)
            print(f"   Available personnel: {len(available_personnel)} - {available_personnel}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing personnel availability: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_bot_authorization():
    """Test delivery bot authorization system"""
    print("\n🔐 Testing Delivery Bot Authorization")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import get_authorized_delivery_ids_from_firebase
        from src.firebase_db import get_data
        
        # Check authorized delivery personnel
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"📊 Authorized delivery personnel: {len(authorized_personnel)}")
        
        if authorized_personnel:
            print("\nAuthorized personnel:")
            for pid, pdata in authorized_personnel.items():
                name = pdata.get('name', 'Unknown')
                telegram_id = pdata.get('telegram_id', 'Unknown')
                status = pdata.get('status', 'Unknown')
                print(f"  {pid}: {name} (TG: {telegram_id}) - Status: {status}")
        
        # Test authorization ID retrieval
        print("\n🔍 Testing authorization ID retrieval...")
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"   Retrieved authorized IDs: {authorized_ids}")
        
        return len(authorized_ids) > 0
        
    except Exception as e:
        print(f"❌ Error testing delivery bot authorization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_order_workflow():
    """Test complete order workflow from creation to broadcast readiness"""
    print("\n🔄 Testing Complete Order Workflow")
    print("=" * 50)
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        from src.firebase_db import get_data, delete_data
        
        # Create comprehensive test order
        test_order_number = f"TEST_WORKFLOW_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_order_data = {
            "order_number": test_order_number,
            "user_id": "test_user_workflow",
            "customer_name": "Workflow Test Customer",
            "restaurant_name": "Workflow Test Restaurant",
            "restaurant_id": "rest_workflow_001",
            "restaurant_area_id": "1",  # Use area 1
            "area_id": "1",
            "delivery_location_id": "loc_workflow_001",
            "delivery_address": "123 Test Street, Test City",
            "subtotal": 150.0,
            "delivery_fee": 35.0,
            "total": 185.0,
            "payment_method": "telebirr",
            "payment_status": "confirmed"
        }
        
        print(f"📦 Testing complete workflow with order: {test_order_number}")
        
        # Step 1: Simulate order confirmation
        print("\n1. Confirming order...")
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.CONFIRMED,
            test_order_data
        )
        
        if not success:
            print("❌ Order confirmation failed")
            return False
        
        print("✅ Order confirmed successfully")
        
        # Step 2: Verify order is in confirmed_orders
        print("\n2. Verifying order persistence...")
        confirmed_order = get_data(f"confirmed_orders/{test_order_number}")
        if not confirmed_order:
            print("❌ Order not found in confirmed_orders collection")
            return False
        
        print("✅ Order found in confirmed_orders collection")
        print(f"   Status: {confirmed_order.get('status')}")
        print(f"   Delivery Status: {confirmed_order.get('delivery_status')}")
        
        # Step 3: Test delivery personnel availability
        print("\n3. Testing delivery personnel availability...")
        delivery_area_id = confirmed_order.get('restaurant_area_id') or confirmed_order.get('area_id')
        available_personnel = find_available_personnel_with_capacity_check(str(delivery_area_id))
        
        print(f"   Delivery area: {delivery_area_id}")
        print(f"   Available personnel: {len(available_personnel)} - {available_personnel}")
        
        # Step 4: Simulate broadcast readiness check
        print("\n4. Testing broadcast readiness...")
        if available_personnel:
            print("✅ Personnel available for broadcast")
            
            # Check personnel data completeness
            from src.firebase_db import get_data
            personnel_data = get_data("delivery_personnel") or {}
            
            broadcast_ready_count = 0
            for personnel_id in available_personnel:
                pdata = personnel_data.get(personnel_id)
                if pdata and pdata.get('telegram_id'):
                    broadcast_ready_count += 1
                    print(f"   ✅ {personnel_id}: Ready for broadcast (TG: {pdata.get('telegram_id')})")
                else:
                    print(f"   ❌ {personnel_id}: Missing telegram_id")
            
            print(f"   Total broadcast-ready personnel: {broadcast_ready_count}")
            
            if broadcast_ready_count > 0:
                print("✅ Order ready for delivery broadcast")
                workflow_success = True
            else:
                print("❌ No personnel ready for broadcast (missing telegram_id)")
                workflow_success = False
        else:
            print("❌ No available personnel for broadcast")
            workflow_success = False
        
        # Clean up
        delete_data(f"confirmed_orders/{test_order_number}")
        print("\n🧹 Workflow test cleanup completed")
        
        return workflow_success
        
    except Exception as e:
        print(f"❌ Error testing complete workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all delivery notification fix tests"""
    print("🚀 Delivery Notification System Fix Validation")
    print("=" * 80)
    
    tests = [
        ("Order Status Manager Fixes", test_order_status_manager_fixes),
        ("Delivery Personnel Availability", test_delivery_personnel_availability),
        ("Delivery Bot Authorization", test_delivery_bot_authorization),
        ("Complete Order Workflow", test_complete_order_workflow),
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_function in tests:
        print(f"\n{'='*80}")
        try:
            result = test_function()
            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - ERROR: {e}")
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 FIX VALIDATION SUMMARY")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
    
    total_tests = len(tests)
    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL FIXES VALIDATED SUCCESSFULLY!")
        print("✅ Delivery notification system should now work correctly.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} tests failed.")
        print("❌ Additional fixes may be needed.")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
