#!/usr/bin/env python3
"""
Setup test delivery personnel for debugging delivery notifications
"""

import sys
import os
import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def setup_test_personnel():
    """Setup test delivery personnel"""
    print("👥 Setting up test delivery personnel...")
    
    try:
        from src.data_models import DeliveryPersonnel
        from src.firebase_db import get_data, set_data
        from src.bots.management_bot import add_authorized_delivery_personnel
        
        # Get existing personnel
        existing_personnel = get_data("delivery_personnel") or {}
        
        # Test personnel data
        test_personnel = [
            {
                "name": "Test Delivery Person 1",
                "phone": "+251911111111",
                "telegram_id": "7729984017",  # Use admin ID for testing
                "email": "<EMAIL>",
                "areas": ["1", "2", "3"]
            },
            {
                "name": "Test Delivery Person 2", 
                "phone": "+251922222222",
                "telegram_id": "5546595738",  # Second authorized ID
                "email": "<EMAIL>",
                "areas": ["3", "4", "5"]
            }
        ]
        
        created_personnel = []
        
        for person_data in test_personnel:
            # Create personnel ID
            personnel_id = f"test_dp_{person_data['telegram_id']}"
            
            # Check if already exists
            if personnel_id in existing_personnel:
                print(f"✅ Personnel {personnel_id} already exists")
                created_personnel.append(personnel_id)
                continue
            
            # Create personnel object
            personnel = DeliveryPersonnel(personnel_id)
            personnel.name = person_data['name']
            personnel.phone_number = person_data['phone']
            personnel.telegram_id = person_data['telegram_id']
            personnel.email = person_data['email']
            personnel.service_areas = person_data['areas']
            personnel.vehicle_type = 'motorcycle'
            personnel.max_capacity = 5
            personnel.current_capacity = 0
            personnel.status = 'available'
            personnel.is_verified = True
            personnel.rating = 5.0
            personnel.total_deliveries = 0
            personnel.successful_deliveries = 0
            personnel.created_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            personnel.last_active = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Add to existing personnel
            existing_personnel[personnel_id] = personnel.to_dict()
            
            print(f"✅ Created personnel: {personnel_id} ({person_data['name']})")
            created_personnel.append(personnel_id)
        
        # Save to Firebase
        success = set_data("delivery_personnel", existing_personnel)
        if success:
            print("✅ Personnel data saved to Firebase")
        else:
            print("❌ Failed to save personnel data")
            return False
        
        # Add to authorized delivery personnel
        print("\n🔐 Adding to authorized delivery personnel...")
        admin_id = 7729984017
        
        for person_data in test_personnel:
            auth_success = add_authorized_delivery_personnel(
                int(person_data['telegram_id']),
                person_data['name'],
                admin_id
            )
            
            if auth_success:
                print(f"✅ Authorized: {person_data['name']} (TG: {person_data['telegram_id']})")
            else:
                print(f"❌ Failed to authorize: {person_data['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up test personnel: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_setup():
    """Verify the test personnel setup"""
    print("\n🔍 Verifying test personnel setup...")
    
    try:
        from src.firebase_db import get_data
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        
        # Check delivery personnel collection
        delivery_personnel = get_data("delivery_personnel") or {}
        test_personnel_count = len([pid for pid in delivery_personnel.keys() if pid.startswith("test_dp_")])
        print(f"📊 Test personnel in database: {test_personnel_count}")
        
        # Check authorized personnel collection
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        authorized_test_count = len([pid for pid in authorized_personnel.keys() if "test" in pid.lower()])
        print(f"🔐 Test personnel authorized: {authorized_test_count}")
        
        # Test availability for each area
        print("\n📍 Testing availability by area:")
        for area_id in ["1", "2", "3", "4", "5"]:
            available = find_available_personnel_with_capacity_check(area_id)
            print(f"   Area {area_id}: {len(available)} available - {available}")
        
        return test_personnel_count > 0 and authorized_test_count > 0
        
    except Exception as e:
        print(f"❌ Error verifying setup: {e}")
        return False

def main():
    """Setup and verify test delivery personnel"""
    print("🚀 Test Delivery Personnel Setup")
    print("=" * 50)
    
    # Setup test personnel
    setup_success = setup_test_personnel()
    
    if setup_success:
        # Verify setup
        verify_success = verify_setup()
        
        if verify_success:
            print("\n🎉 Test personnel setup completed successfully!")
            print("✅ Ready for delivery notification testing")
            return 0
        else:
            print("\n❌ Setup verification failed")
            return 1
    else:
        print("\n❌ Test personnel setup failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
