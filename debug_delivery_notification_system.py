#!/usr/bin/env python3
"""
Comprehensive Delivery Notification System Debugger
Diagnoses issues with delivery personnel notifications and order persistence
"""

import sys
import os
import datetime
import traceback

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_firebase_collections():
    """Test 1: Check Firebase collections and order persistence"""
    print("🔍 Test 1: Firebase Collections and Order Persistence")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data, delete_data
        
        # Check confirmed_orders collection
        print("📦 Checking confirmed_orders collection...")
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"   Found {len(confirmed_orders)} confirmed orders")
        
        if confirmed_orders:
            print("   Recent orders:")
            for order_num, order_data in list(confirmed_orders.items())[:3]:
                status = order_data.get('status', 'Unknown')
                delivery_status = order_data.get('delivery_status', 'Unknown')
                print(f"     Order #{order_num}: Status={status}, Delivery={delivery_status}")
        
        # Check pending_admin_reviews collection
        print("\n📋 Checking pending_admin_reviews collection...")
        pending_reviews = get_data("pending_admin_reviews") or {}
        print(f"   Found {len(pending_reviews)} pending reviews")
        
        # Check delivery_personnel collection
        print("\n👥 Checking delivery_personnel collection...")
        delivery_personnel = get_data("delivery_personnel") or {}
        print(f"   Found {len(delivery_personnel)} delivery personnel")
        
        if delivery_personnel:
            print("   Personnel status:")
            for pid, pdata in delivery_personnel.items():
                name = pdata.get('name', 'Unknown')
                status = pdata.get('status', 'Unknown')
                verified = pdata.get('is_verified', False)
                capacity = pdata.get('current_capacity', 0)
                max_cap = pdata.get('max_capacity', 5)
                print(f"     {pid}: {name} - Status: {status}, Verified: {verified}, Capacity: {capacity}/{max_cap}")
        
        # Check authorized_delivery_personnel collection
        print("\n🔐 Checking authorized_delivery_personnel collection...")
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"   Found {len(authorized_personnel)} authorized personnel")
        
        if authorized_personnel:
            print("   Authorized personnel:")
            for pid, pdata in authorized_personnel.items():
                name = pdata.get('name', 'Unknown')
                telegram_id = pdata.get('telegram_id', 'Unknown')
                status = pdata.get('status', 'Unknown')
                print(f"     {pid}: {name} (TG: {telegram_id}) - Status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Firebase collections: {e}")
        traceback.print_exc()
        return False

def test_delivery_bot_configuration():
    """Test 2: Check delivery bot configuration and authorization"""
    print("\n🤖 Test 2: Delivery Bot Configuration")
    print("=" * 60)
    
    try:
        from src.config import DELIVERY_BOT_TOKEN, DELIVERY_BOT_AUTHORIZED_IDS
        
        print(f"📱 Delivery Bot Token: {DELIVERY_BOT_TOKEN[:20]}..." if DELIVERY_BOT_TOKEN else "❌ No token configured")
        print(f"🔐 Fallback Authorized IDs: {DELIVERY_BOT_AUTHORIZED_IDS}")
        
        # Test delivery bot authorization system
        print("\n🔍 Testing delivery bot authorization system...")
        from src.bots.delivery_bot import get_authorized_delivery_ids_from_firebase
        
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"   Total authorized IDs: {len(authorized_ids)}")
        print(f"   Authorized IDs: {authorized_ids}")
        
        # Test delivery bot initialization
        print("\n🚀 Testing delivery bot initialization...")
        import telebot
        delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)
        
        # Test bot info
        bot_info = delivery_bot.get_me()
        print(f"   Bot username: @{bot_info.username}")
        print(f"   Bot ID: {bot_info.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing delivery bot configuration: {e}")
        traceback.print_exc()
        return False

def test_personnel_availability_system():
    """Test 3: Check personnel availability and capacity system"""
    print("\n👥 Test 3: Personnel Availability System")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import (
            find_available_personnel_with_capacity_check,
            get_personnel_current_capacity
        )
        
        # Test for different areas
        test_areas = ['1', '2', '3', '4', '5', 'area_bole', 'area_4kilo']
        
        for area_id in test_areas:
            print(f"\n📍 Testing area: {area_id}")
            available_personnel = find_available_personnel_with_capacity_check(area_id)
            print(f"   Available personnel: {len(available_personnel)} - {available_personnel}")
            
            # Check capacity for each available personnel
            for personnel_id in available_personnel:
                try:
                    capacity = get_personnel_current_capacity(personnel_id)
                    print(f"     {personnel_id}: Current capacity = {capacity}")
                except Exception as e:
                    print(f"     {personnel_id}: Error getting capacity - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing personnel availability: {e}")
        traceback.print_exc()
        return False

def test_order_broadcast_workflow():
    """Test 4: Test the complete order broadcast workflow"""
    print("\n📡 Test 4: Order Broadcast Workflow")
    print("=" * 60)
    
    try:
        # Create a test order
        test_order_number = f"TEST_BROADCAST_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_order_data = {
            "order_number": test_order_number,
            "user_id": "test_user",
            "customer_name": "Test Customer",
            "restaurant_name": "Test Restaurant",
            "restaurant_id": "rest_001",
            "restaurant_area_id": "1",  # Use area 1 for testing
            "area_id": "1",
            "delivery_location_id": "loc_001",
            "delivery_address": "Test Address",
            "subtotal": 100.0,
            "delivery_fee": 30.0,
            "total": 130.0,
            "status": "CONFIRMED",
            "delivery_status": "pending_assignment",
            "confirmed_at": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        print(f"📦 Created test order: {test_order_number}")
        
        # Save test order to confirmed_orders collection
        from src.firebase_db import set_data
        success = set_data(f"confirmed_orders/{test_order_number}", test_order_data)
        
        if success:
            print("✅ Test order saved to confirmed_orders collection")
        else:
            print("❌ Failed to save test order")
            return False
        
        # Test the broadcast logic
        print("\n📡 Testing broadcast logic...")
        
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        from src.config import DELIVERY_BOT_TOKEN
        import telebot
        
        delivery_area_id = test_order_data.get('restaurant_area_id') or test_order_data.get('area_id')
        print(f"   Delivery area ID: {delivery_area_id}")
        
        # Find available personnel
        available_personnel_ids = find_available_personnel_with_capacity_check(str(delivery_area_id))
        print(f"   Found {len(available_personnel_ids)} available personnel: {available_personnel_ids}")
        
        if not available_personnel_ids:
            print("⚠️ No available personnel found for broadcast test")
            return False
        
        # Test message creation (without sending)
        print("\n📝 Testing message creation...")
        
        delivery_message = f"""
🚨 **NEW ORDER AVAILABLE** 🚨

📋 **Order #{test_order_number}**
🏪 **Restaurant:** {test_order_data['restaurant_name']}
📍 **Delivery Address:** {test_order_data['delivery_address']}
💰 **Delivery Fee:** {test_order_data['delivery_fee']} birr
📦 **Order Total:** {test_order_data['total']} birr

⏰ **Order Time:** {test_order_data['confirmed_at']}

Would you like to accept this delivery?
"""
        
        print("✅ Message created successfully")
        print(f"   Message length: {len(delivery_message)} characters")
        
        # Test delivery bot initialization for broadcast
        print("\n🤖 Testing delivery bot for broadcast...")
        delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)
        
        # Get fresh delivery personnel data
        from src.firebase_db import get_data
        fresh_personnel_data = get_data("delivery_personnel") or {}
        
        broadcast_count = 0
        for personnel_id in available_personnel_ids:
            personnel_data = fresh_personnel_data.get(personnel_id)
            if personnel_data:
                telegram_id = personnel_data.get('telegram_id')
                if telegram_id:
                    print(f"   Would broadcast to: {personnel_id} (TG: {telegram_id})")
                    broadcast_count += 1
                else:
                    print(f"   ⚠️ No Telegram ID for personnel: {personnel_id}")
            else:
                print(f"   ⚠️ Personnel data not found: {personnel_id}")
        
        print(f"   Total broadcasts that would be sent: {broadcast_count}")
        
        # Clean up test order
        from src.firebase_db import delete_data
        delete_data(f"confirmed_orders/{test_order_number}")
        print(f"🧹 Cleaned up test order")
        
        return broadcast_count > 0
        
    except Exception as e:
        print(f"❌ Error testing order broadcast workflow: {e}")
        traceback.print_exc()
        return False

def test_order_status_manager():
    """Test 5: Test order status manager integration"""
    print("\n📊 Test 5: Order Status Manager Integration")
    print("=" * 60)
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus
        
        # Test order status manager functionality
        print("🔍 Testing order status manager...")
        
        # Create a test order for status management
        test_order_number = f"TEST_STATUS_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_order_data = {
            "order_number": test_order_number,
            "user_id": "test_user",
            "restaurant_name": "Test Restaurant",
            "status": "pending_review"
        }
        
        # Save initial order
        from src.firebase_db import set_data
        set_data(f"pending_admin_reviews/{test_order_number}", test_order_data)
        
        # Test status update to CONFIRMED
        print(f"📝 Testing status update to CONFIRMED...")
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.CONFIRMED,
            {"delivery_status": "pending_assignment"}
        )
        
        if success:
            print("✅ Order status updated successfully")
            
            # Verify the order is in confirmed_orders collection
            from src.firebase_db import get_data
            confirmed_order = get_data(f"confirmed_orders/{test_order_number}")
            
            if confirmed_order:
                print("✅ Order found in confirmed_orders collection")
                print(f"   Status: {confirmed_order.get('status')}")
                print(f"   Delivery Status: {confirmed_order.get('delivery_status')}")
            else:
                print("❌ Order not found in confirmed_orders collection")
                return False
        else:
            print("❌ Order status update failed")
            return False
        
        # Clean up
        from src.firebase_db import delete_data
        delete_data(f"confirmed_orders/{test_order_number}")
        delete_data(f"pending_admin_reviews/{test_order_number}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order status manager: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive delivery notification system diagnostics"""
    print("🚀 Wiz-Aroma Delivery Notification System Diagnostics")
    print("=" * 80)
    
    tests = [
        ("Firebase Collections", test_firebase_collections),
        ("Delivery Bot Configuration", test_delivery_bot_configuration),
        ("Personnel Availability System", test_personnel_availability_system),
        ("Order Broadcast Workflow", test_order_broadcast_workflow),
        ("Order Status Manager", test_order_status_manager),
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_function in tests:
        print(f"\n{'='*80}")
        try:
            result = test_function()
            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - ERROR: {e}")
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
    
    total_tests = len(tests)
    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL DIAGNOSTICS PASSED!")
        print("✅ Delivery notification system appears to be working correctly.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} diagnostics failed.")
        print("❌ Issues found in delivery notification system.")
        
        # Provide specific recommendations
        print("\n🔧 RECOMMENDATIONS:")
        if not results.get("Firebase Collections", True):
            print("- Check Firebase connection and collection structure")
        if not results.get("Delivery Bot Configuration", True):
            print("- Verify delivery bot token and authorization configuration")
        if not results.get("Personnel Availability System", True):
            print("- Check delivery personnel registration and availability status")
        if not results.get("Order Broadcast Workflow", True):
            print("- Debug order broadcast logic and personnel selection")
        if not results.get("Order Status Manager", True):
            print("- Fix order status transitions and collection management")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
