#!/usr/bin/env python3
"""
Comprehensive System Integration Test for Wiz Aroma Delivery System
Tests the complete order lifecycle with enhanced error handling and data validation
"""

import sys
import os
import datetime
import time
import json
from typing import Dict, Any, List

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_handling():
    """Test the enhanced signal handling and graceful shutdown"""
    print("🔧 Testing Signal Handling and Graceful Shutdown...")
    
    try:
        # Test interruptible sleep function
        from main import interruptible_sleep, shutdown_event
        
        print("  ✅ Testing interruptible sleep...")
        start_time = time.time()
        result = interruptible_sleep(2)  # 2 second sleep
        elapsed = time.time() - start_time
        
        if result and 1.8 <= elapsed <= 2.2:
            print(f"    ✅ Interruptible sleep working correctly ({elapsed:.1f}s)")
        else:
            print(f"    ⚠️ Interruptible sleep timing issue ({elapsed:.1f}s)")
        
        # Test shutdown event
        print("  ✅ Testing shutdown event mechanism...")
        if not shutdown_event.is_set():
            print("    ✅ Shutdown event properly initialized")
        else:
            print("    ⚠️ Shutdown event already set")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing signal handling: {e}")
        return False

def test_order_status_manager():
    """Test the order status manager functionality"""
    print("📊 Testing Order Status Manager...")
    
    try:
        from src.utils.order_status_manager import order_status_manager, OrderStatus, DeliveryStatus
        
        # Test order status validation
        print("  ✅ Testing order status transitions...")
        
        # Create a test order
        test_order_number = f"TEST_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_order_data = {
            "order_number": test_order_number,
            "user_id": "test_user",
            "restaurant_name": "Test Restaurant",
            "subtotal": 50.0,
            "delivery_fee": 30.0,
            "total": 80.0,
            "status": "pending_review"
        }
        
        # Test status update
        success = order_status_manager.update_order_status(
            test_order_number,
            OrderStatus.APPROVED,
            test_order_data
        )
        
        if success:
            print(f"    ✅ Order status update successful for {test_order_number}")
        else:
            print(f"    ⚠️ Order status update failed for {test_order_number}")
        
        # Test order data retrieval
        retrieved_data = order_status_manager.get_order_data(test_order_number)
        if retrieved_data:
            print(f"    ✅ Order data retrieval successful")
        else:
            print(f"    ⚠️ Order data retrieval failed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing order status manager: {e}")
        return False

def test_analytics_validator():
    """Test the analytics validator functionality"""
    print("📈 Testing Analytics Validator...")
    
    try:
        from src.utils.analytics_validator import analytics_validator
        
        print("  ✅ Testing revenue calculations validation...")
        
        # Run revenue validation
        validation_results = analytics_validator.validate_revenue_calculations()
        
        if validation_results and "revenue_validation" in validation_results:
            print("    ✅ Revenue validation completed")
            
            # Check validation results
            summary = validation_results.get("summary", {})
            overall_status = summary.get("overall_status", "unknown")
            
            if overall_status == "passed":
                print("    ✅ All revenue calculations validated successfully")
            elif overall_status == "warning":
                print("    ⚠️ Revenue validation completed with warnings")
            else:
                print("    ❌ Revenue validation failed")
            
            # Check earnings validation
            earnings_validation = validation_results.get("earnings_validation", {})
            discrepancies = earnings_validation.get("discrepancies", [])
            
            if discrepancies:
                print(f"    ⚠️ Found {len(discrepancies)} earnings discrepancies")
            else:
                print("    ✅ No earnings discrepancies found")
        else:
            print("    ❌ Revenue validation failed to complete")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing analytics validator: {e}")
        return False

def test_data_consistency_validator():
    """Test the data consistency validator"""
    print("🔍 Testing Data Consistency Validator...")
    
    try:
        from src.utils.data_consistency_validator import data_consistency_validator
        
        print("  ✅ Testing order lifecycle consistency...")
        
        # Run consistency validation
        validation_results = data_consistency_validator.validate_order_lifecycle_consistency()
        
        if validation_results and "consistency_errors" in validation_results:
            errors = validation_results["consistency_errors"]
            total_orders = validation_results.get("total_orders_checked", 0)
            
            print(f"    ✅ Checked {total_orders} orders for consistency")
            
            if not errors:
                print("    ✅ No consistency errors found")
            else:
                print(f"    ⚠️ Found {len(errors)} consistency issues")
                
                # Categorize errors
                error_types = {}
                for error in errors:
                    error_type = error.get("type", "unknown")
                    error_types[error_type] = error_types.get(error_type, 0) + 1
                
                for error_type, count in error_types.items():
                    print(f"      - {error_type}: {count} issues")
        else:
            print("    ❌ Consistency validation failed to complete")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing data consistency validator: {e}")
        return False

def test_realtime_sync_manager():
    """Test the real-time sync manager"""
    print("⚡ Testing Real-time Sync Manager...")
    
    try:
        from src.utils.realtime_sync_manager import realtime_sync_manager
        
        print("  ✅ Testing sync manager initialization...")
        
        # Start sync manager
        realtime_sync_manager.start_sync_manager()
        time.sleep(1)  # Give it time to start
        
        if realtime_sync_manager.running:
            print("    ✅ Sync manager started successfully")
        else:
            print("    ⚠️ Sync manager failed to start")
        
        # Test cache functionality
        print("  ✅ Testing cache functionality...")
        test_order_number = f"CACHE_TEST_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # This should return None for non-existent order
        cached_data = realtime_sync_manager.get_order_data(test_order_number)
        if cached_data is None:
            print("    ✅ Cache correctly returns None for non-existent order")
        else:
            print("    ⚠️ Cache returned unexpected data for non-existent order")
        
        # Test cache invalidation
        realtime_sync_manager.invalidate_cache()
        print("    ✅ Cache invalidation completed")
        
        # Stop sync manager
        realtime_sync_manager.stop_sync_manager()
        print("    ✅ Sync manager stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing real-time sync manager: {e}")
        return False

def test_firebase_integration():
    """Test Firebase integration and data operations"""
    print("🔥 Testing Firebase Integration...")
    
    try:
        from src.firebase_db import get_data, set_data, delete_data
        
        print("  ✅ Testing Firebase connectivity...")
        
        # Test basic read operation
        test_data = get_data("areas")
        if test_data is not None:
            print("    ✅ Firebase read operation successful")
        else:
            print("    ⚠️ Firebase read operation returned None")
        
        # Test write operation with test data
        test_key = f"system_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_value = {
            "test": True,
            "timestamp": datetime.datetime.now().isoformat(),
            "purpose": "system_integration_test"
        }
        
        write_success = set_data(f"system_tests/{test_key}", test_value)
        if write_success:
            print("    ✅ Firebase write operation successful")
            
            # Test read back
            read_back = get_data(f"system_tests/{test_key}")
            if read_back and read_back.get("test") == True:
                print("    ✅ Firebase read-back verification successful")
                
                # Clean up test data
                delete_success = delete_data(f"system_tests/{test_key}")
                if delete_success:
                    print("    ✅ Firebase cleanup successful")
                else:
                    print("    ⚠️ Firebase cleanup failed")
            else:
                print("    ❌ Firebase read-back verification failed")
        else:
            print("    ❌ Firebase write operation failed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing Firebase integration: {e}")
        return False

def test_earnings_calculations():
    """Test earnings calculations and 50% sharing"""
    print("💰 Testing Earnings Calculations...")
    
    try:
        from src.utils.earnings_utils import update_personnel_earnings, get_personnel_earnings_summary
        
        print("  ✅ Testing 50% delivery fee sharing...")
        
        # Test earnings update
        test_personnel_id = f"TEST_PERSONNEL_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        test_delivery_fee = 30.0
        expected_earnings = test_delivery_fee * 0.5  # 50% sharing
        
        success = update_personnel_earnings(test_personnel_id, test_delivery_fee)
        
        if success:
            print(f"    ✅ Earnings update successful for {test_personnel_id}")
            
            # Verify earnings calculation
            earnings_summary = get_personnel_earnings_summary(test_personnel_id)
            if earnings_summary:
                daily_earnings = earnings_summary.get("daily_earnings", 0)
                if abs(daily_earnings - expected_earnings) < 0.01:  # Allow for floating point precision
                    print(f"    ✅ 50% sharing calculation correct: {daily_earnings} birr from {test_delivery_fee} birr delivery fee")
                else:
                    print(f"    ❌ 50% sharing calculation incorrect: expected {expected_earnings}, got {daily_earnings}")
            else:
                print("    ❌ Failed to retrieve earnings summary")
        else:
            print(f"    ❌ Earnings update failed for {test_personnel_id}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing earnings calculations: {e}")
        return False

def main():
    """Run comprehensive system integration tests"""
    print("🚀 Wiz Aroma Delivery System - Comprehensive Integration Test")
    print("=" * 80)
    
    tests = [
        ("Signal Handling", test_signal_handling),
        ("Order Status Manager", test_order_status_manager),
        ("Analytics Validator", test_analytics_validator),
        ("Data Consistency Validator", test_data_consistency_validator),
        ("Real-time Sync Manager", test_realtime_sync_manager),
        ("Firebase Integration", test_firebase_integration),
        ("Earnings Calculations", test_earnings_calculations),
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_function in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        
        try:
            result = test_function()
            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name} - ERROR: {e}")
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
    
    print(f"\nTotal Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! System integration is working correctly.")
        return 0
    else:
        print(f"\n⚠️ {total_tests - passed_tests} tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
