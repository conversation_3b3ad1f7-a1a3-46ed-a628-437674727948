"""
Real-time Synchronization Manager for Wiz Aroma Delivery System
Ensures all bot notifications receive consistent and up-to-date order information
"""

import datetime
import threading
import time
import logging
from typing import Dict, Any, List, Optional, Callable
from collections import defaultdict

from src.firebase_db import get_data, set_data, delete_data
from src.config import logger


class RealtimeSyncManager:
    """Manages real-time data synchronization across all bots"""
    
    def __init__(self):
        self.logger = logger
        self.sync_lock = threading.RLock()
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 30  # 30 seconds cache TTL
        self.subscribers = defaultdict(list)  # Event subscribers
        self.sync_queue = []
        self.sync_thread = None
        self.running = False
    
    def start_sync_manager(self):
        """Start the real-time sync manager"""
        try:
            if self.running:
                return
            
            self.running = True
            self.sync_thread = threading.Thread(target=self._sync_worker, daemon=True)
            self.sync_thread.start()
            self.logger.info("Real-time sync manager started")
            
        except Exception as e:
            self.logger.error(f"Error starting sync manager: {e}")
    
    def stop_sync_manager(self):
        """Stop the real-time sync manager"""
        try:
            self.running = False
            if self.sync_thread and self.sync_thread.is_alive():
                self.sync_thread.join(timeout=5)
            self.logger.info("Real-time sync manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping sync manager: {e}")
    
    def get_order_data(self, order_number: str, force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """Get order data with caching and real-time updates"""
        try:
            cache_key = f"order_{order_number}"
            
            # Check cache first (unless force refresh)
            if not force_refresh and self._is_cache_valid(cache_key):
                return self.cache.get(cache_key)
            
            # Get fresh data from Firebase
            order_data = self._fetch_order_from_firebase(order_number)
            
            if order_data:
                # Update cache
                with self.sync_lock:
                    self.cache[cache_key] = order_data
                    self.cache_timestamps[cache_key] = time.time()
                
                # Notify subscribers of data update
                self._notify_subscribers('order_updated', {
                    'order_number': order_number,
                    'order_data': order_data
                })
            
            return order_data
            
        except Exception as e:
            self.logger.error(f"Error getting order data for {order_number}: {e}")
            return None
    
    def update_order_data(self, order_number: str, order_data: Dict[str, Any], 
                         collection: str = "confirmed_orders") -> bool:
        """Update order data with real-time synchronization"""
        try:
            # Update in Firebase
            success = set_data(f"{collection}/{order_number}", order_data)
            
            if success:
                # Update cache immediately
                cache_key = f"order_{order_number}"
                with self.sync_lock:
                    self.cache[cache_key] = order_data
                    self.cache_timestamps[cache_key] = time.time()
                
                # Queue sync notification for all bots
                self._queue_sync_notification({
                    'type': 'order_update',
                    'order_number': order_number,
                    'collection': collection,
                    'timestamp': datetime.datetime.now().isoformat()
                })
                
                self.logger.info(f"Updated order data for {order_number} in {collection}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating order data for {order_number}: {e}")
            return False
    
    def invalidate_cache(self, pattern: str = None):
        """Invalidate cache entries"""
        try:
            with self.sync_lock:
                if pattern:
                    # Invalidate specific pattern
                    keys_to_remove = [key for key in self.cache.keys() if pattern in key]
                    for key in keys_to_remove:
                        del self.cache[key]
                        if key in self.cache_timestamps:
                            del self.cache_timestamps[key]
                    self.logger.info(f"Invalidated {len(keys_to_remove)} cache entries matching '{pattern}'")
                else:
                    # Clear all cache
                    self.cache.clear()
                    self.cache_timestamps.clear()
                    self.logger.info("Cleared all cache entries")
                    
        except Exception as e:
            self.logger.error(f"Error invalidating cache: {e}")
    
    def subscribe_to_updates(self, event_type: str, callback: Callable):
        """Subscribe to real-time updates"""
        try:
            self.subscribers[event_type].append(callback)
            self.logger.info(f"Added subscriber for {event_type} events")
            
        except Exception as e:
            self.logger.error(f"Error subscribing to updates: {e}")
    
    def ensure_data_consistency(self, order_number: str) -> Dict[str, Any]:
        """Ensure data consistency across all collections for an order"""
        try:
            consistency_report = {
                "order_number": order_number,
                "collections_checked": [],
                "inconsistencies": [],
                "status": "consistent"
            }
            
            # Check all possible collections
            collections = ["confirmed_orders", "completed_orders", "pending_admin_reviews", "awaiting_receipt"]
            order_instances = {}
            
            for collection in collections:
                order_data = get_data(f"{collection}/{order_number}")
                if order_data:
                    order_instances[collection] = order_data
                    consistency_report["collections_checked"].append(collection)
            
            # Check for inconsistencies
            if len(order_instances) > 1:
                # Order exists in multiple collections - potential issue
                consistency_report["inconsistencies"].append({
                    "type": "multiple_collections",
                    "collections": list(order_instances.keys()),
                    "severity": "high"
                })
                consistency_report["status"] = "inconsistent"
            
            # Check status consistency within each instance
            for collection, order_data in order_instances.items():
                status_issues = self._check_status_consistency(order_data)
                if status_issues:
                    consistency_report["inconsistencies"].extend(status_issues)
                    consistency_report["status"] = "inconsistent"
            
            return consistency_report
            
        except Exception as e:
            self.logger.error(f"Error checking data consistency for {order_number}: {e}")
            return {"status": "error", "error": str(e)}
    
    def _fetch_order_from_firebase(self, order_number: str) -> Optional[Dict[str, Any]]:
        """Fetch order data from Firebase collections"""
        collections = ["confirmed_orders", "completed_orders", "pending_admin_reviews", "awaiting_receipt"]
        
        for collection in collections:
            order_data = get_data(f"{collection}/{order_number}")
            if order_data:
                # Add collection info to order data
                order_data['_source_collection'] = collection
                return order_data
        
        return None
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid"""
        if cache_key not in self.cache or cache_key not in self.cache_timestamps:
            return False
        
        age = time.time() - self.cache_timestamps[cache_key]
        return age < self.cache_ttl
    
    def _queue_sync_notification(self, notification: Dict[str, Any]):
        """Queue a sync notification for processing"""
        try:
            with self.sync_lock:
                self.sync_queue.append(notification)
                
        except Exception as e:
            self.logger.error(f"Error queuing sync notification: {e}")
    
    def _sync_worker(self):
        """Background worker for processing sync notifications"""
        while self.running:
            try:
                # Process sync queue
                notifications_to_process = []
                with self.sync_lock:
                    notifications_to_process = self.sync_queue.copy()
                    self.sync_queue.clear()
                
                for notification in notifications_to_process:
                    self._process_sync_notification(notification)
                
                # Clean up old cache entries
                self._cleanup_cache()
                
                # Sleep for a short interval
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in sync worker: {e}")
                time.sleep(5)  # Wait longer on error
    
    def _process_sync_notification(self, notification: Dict[str, Any]):
        """Process a sync notification"""
        try:
            notification_type = notification.get('type')
            
            if notification_type == 'order_update':
                order_number = notification.get('order_number')
                
                # Notify all subscribers
                self._notify_subscribers('order_updated', notification)
                
                # Trigger cache refresh for related data
                self.invalidate_cache(f"order_{order_number}")
                
        except Exception as e:
            self.logger.error(f"Error processing sync notification: {e}")
    
    def _notify_subscribers(self, event_type: str, data: Dict[str, Any]):
        """Notify all subscribers of an event"""
        try:
            subscribers = self.subscribers.get(event_type, [])
            for callback in subscribers:
                try:
                    callback(data)
                except Exception as e:
                    self.logger.error(f"Error in subscriber callback: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error notifying subscribers: {e}")
    
    def _cleanup_cache(self):
        """Clean up expired cache entries"""
        try:
            current_time = time.time()
            expired_keys = []
            
            with self.sync_lock:
                for key, timestamp in self.cache_timestamps.items():
                    if current_time - timestamp > self.cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    if key in self.cache:
                        del self.cache[key]
                    if key in self.cache_timestamps:
                        del self.cache_timestamps[key]
            
            if expired_keys:
                self.logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up cache: {e}")
    
    def _check_status_consistency(self, order_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check status consistency within order data"""
        issues = []
        
        try:
            status = order_data.get('status')
            delivery_status = order_data.get('delivery_status')
            
            # Check for missing required fields
            if not status:
                issues.append({
                    "type": "missing_status",
                    "field": "status",
                    "severity": "high"
                })
            
            # Check status/delivery_status consistency
            if status == 'CONFIRMED' and not delivery_status:
                issues.append({
                    "type": "missing_delivery_status",
                    "field": "delivery_status",
                    "severity": "medium"
                })
            
            # Check timestamp consistency
            if delivery_status == 'completed' and not order_data.get('completed_at'):
                issues.append({
                    "type": "missing_timestamp",
                    "field": "completed_at",
                    "severity": "medium"
                })
            
        except Exception as e:
            self.logger.error(f"Error checking status consistency: {e}")
        
        return issues


# Global instance
realtime_sync_manager = RealtimeSyncManager()
